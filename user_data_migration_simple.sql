-- 用户数据迁移脚本 - 简化版本
-- 一次性执行所有操作

USE CATALOG paimon_catalog;

-- 设置Flink配置以解决Paimon兼容性问题
SET 'execution.checkpointing.mode' = 'EXACTLY_ONCE';
SET 'execution.checkpointing.interval' = '10s';
SET 'pipeline.operator-chaining' = 'true';
SET 'table.exec.sink.not-null-enforcer' = 'DROP';
SET 'table.exec.sink.upsert-materialize' = 'NONE';

-- 关闭自适应并行度模式
SET 'jobmanager.adaptive-scheduler.enabled' = 'false';
SET 'execution.runtime-mode' = 'BATCH';

-- 创建临时表
CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_user_info_temp` (
    USER_ID STRING NOT NULL,
    IN_DATE STRING,
    OPEN_DATE STRING,
    DESTROY_TIME STRING,
    paimon_ingest_time TIMESTAMP(3),
    PRIMARY KEY (USER_ID) NOT ENFORCED
) WITH (
    'connector' = 'paimon',
    'path' = 'hdfs:///user/hh_slfn2_sschj/paimon/ubd_sscj_prod_flink/dwd_r_paimon_user_info_temp',
    'auto-create' = 'true',
    'merge-engine' = 'partial-update',
    'sequence.field' = 'paimon_ingest_time',
    'bucket' = '64',
    'sink.parallelism' = '4'
);

-- 一步完成：筛选用户并获取完整信息
INSERT INTO `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_user_info_temp`
SELECT
    t1.USER_ID,
    t2.IN_DATE,
    t2.OPEN_DATE,
    t2.DESTROY_TIME,
    CAST(CURRENT_TIMESTAMP AS TIMESTAMP(3)) AS paimon_ingest_time
FROM `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_tf_f_user` t1
LEFT JOIN `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_paimon_human_user_wide` t2
    ON t1.USER_ID = t2.USER_ID
WHERE LENGTH(t1.OPEN_DATE) = 12
  AND t1.USER_ID IS NOT NULL;

-- 更新原表
INSERT INTO `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_tf_f_user`
SELECT
    t1.PARTITION_ID, t1.USER_ID, t1.CUST_ID, t1.USECUST_ID, t1.BRAND_CODE, t1.PRODUCT_ID,
    t1.EPARCHY_CODE, t1.CITY_CODE, t1.USER_PASSWD, t1.USER_DIFF_CODE, t1.USER_TYPE_CODE,
    t1.SERIAL_NUMBER, t1.NET_TYPE_CODE, t1.SCORE_VALUE, t1.CREDIT_CLASS, t1.BASIC_CREDIT_VALUE,
    t1.CREDIT_VALUE, t1.ACCT_TAG, t1.PREPAY_TAG,
    COALESCE(t2.IN_DATE, t1.IN_DATE) AS IN_DATE,
    COALESCE(t2.OPEN_DATE, t1.OPEN_DATE) AS OPEN_DATE,
    t1.OPEN_MODE, t1.OPEN_DEPART_ID, t1.OPEN_STAFF_ID, t1.IN_DEPART_ID, t1.IN_STAFF_ID,
    t1.REMOVE_TAG,
    COALESCE(t2.DESTROY_TIME, t1.DESTROY_TIME) AS DESTROY_TIME,
    t1.REMOVE_EPARCHY_CODE, t1.REMOVE_CITY_CODE, t1.REMOVE_DEPART_ID, t1.REMOVE_REASON_CODE,
    t1.MODIFY_TAG, t1.UPDATE_TIME, t1.UPDATE_DEPART_ID, t1.UPDATE_STAFF_ID, t1.RSRV_NUM1,
    t1.RSRV_NUM2, t1.RSRV_NUM3, t1.RSRV_NUM4, t1.RSRV_NUM5, t1.RSRV_STR1, t1.RSRV_STR2,
    t1.RSRV_STR3, t1.RSRV_STR4, t1.RSRV_STR5, t1.RSRV_STR6, t1.RSRV_STR7, t1.RSRV_STR8,
    t1.RSRV_STR9, t1.RSRV_STR10, t1.RSRV_DATE1, t1.RSRV_DATE2, t1.RSRV_DATE3, t1.RSRV_TAG1,
    t1.RSRV_TAG2, t1.RSRV_TAG3, t1.PROVINCE_CODE, t1.opt, t1.opttime, t1.in_time, t1.datasource,
    t1.cdhtime, t1.database_tag, t1.kafka_in_time, t1.kafka_out_time,
    CAST(CURRENT_TIMESTAMP AS TIMESTAMP(3)) AS paimon_time, t1.headers
FROM `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_tf_f_user` t1
INNER JOIN `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_user_info_temp` t2
    ON t1.USER_ID = t2.USER_ID;

-- 验证结果
SELECT
    COUNT(*) AS processed_records
FROM `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_user_info_temp`;
