-- 用户数据迁移和更新脚本
-- 目标：创建新的Paimon表，从ods_r_paimon_tf_f_user和dwa_r_paimon_human_user_wide表导入数据，并更新回ods_r_paimon_tf_f_user表

-- 使用Paimon Catalog
USE CATALOG paimon_catalog;

-- 设置Flink配置以解决Paimon兼容性问题
SET 'execution.checkpointing.mode' = 'EXACTLY_ONCE';
SET 'execution.checkpointing.interval' = '10s';
SET 'pipeline.operator-chaining' = 'true';
SET 'table.exec.sink.not-null-enforcer' = 'DROP';
SET 'table.exec.sink.upsert-materialize' = 'NONE';

-- 关闭自适应并行度模式
SET 'jobmanager.adaptive-scheduler.enabled' = 'false';
SET 'execution.runtime-mode' = 'BATCH';

-- 1. 创建新的Paimon表
CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_user_info_temp` (
    USER_ID STRING NOT NULL,
    IN_DATE STRING,
    OPEN_DATE STRING,
    DESTROY_TIME STRING,
    paimon_ingest_time TIMESTAMP(3) COMMENT 'Paimon 记录写入处理时间戳',
    PRIMARY KEY (USER_ID) NOT ENFORCED
) WITH (
    'connector' = 'paimon',
    'path' = 'hdfs:///user/hh_slfn2_sschj/paimon/ubd_sscj_prod_flink/dwd_r_paimon_user_info_temp',
    'auto-create' = 'true',
    'merge-engine' = 'partial-update',
    'sequence.field' = 'paimon_ingest_time',
    'partial-update.ignore-delete' = 'true',
    'changelog-producer' = 'input',
    'bucket' = '64',
    'snapshot.time-retained' = '6h',
    'file.format' = 'avro',
    'metadata.stats-mode' = 'none',
    'consumer.expiration-time' = '72h',
    'sink.parallelism' = '4'
);

-- 2. 从ods_r_paimon_tf_f_user表中筛选OPEN_DATE字段长度为12的用户ID并插入新表
INSERT INTO `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_user_info_temp` (
    USER_ID,
    paimon_ingest_time
)
SELECT
    USER_ID,
    CAST(CURRENT_TIMESTAMP AS TIMESTAMP(3)) AS paimon_ingest_time
FROM `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_tf_f_user`
WHERE CHAR_LENGTH(OPEN_DATE)  in ('10','11','12','14','15')  AND NET_TYPE_CODE IN ('33','30', '50')
  AND USER_ID IS NOT NULL;

-- 3. 从dwa_r_paimon_human_user_wide表中获取字段信息并更新到新表
-- 使用MERGE语句进行UPSERT操作
INSERT INTO `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_user_info_temp` (
    USER_ID,
    IN_DATE,
    OPEN_DATE,
    DESTROY_TIME,
    paimon_ingest_time
)
SELECT
    t1.USER_ID,
    COALESCE(t2.IN_DATE, t1.IN_DATE) AS IN_DATE,
    COALESCE(t2.OPEN_DATE, t1.OPEN_DATE) AS OPEN_DATE,
    COALESCE(t2.DESTROY_TIME, t1.DESTROY_TIME) AS DESTROY_TIME,
    CAST(CURRENT_TIMESTAMP AS TIMESTAMP(3)) AS paimon_ingest_time
FROM `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_user_info_temp` t1
LEFT JOIN `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_paimon_human_user_wide` t2
    ON t1.USER_ID = t2.USER_ID
WHERE t2.USER_ID IS NOT NULL;

-- 4. 将新表中的数据更新回ods_r_paimon_tf_f_user表
-- 由于Paimon表的特性，我们使用INSERT语句进行更新
INSERT INTO `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_tf_f_user` (
   PARTITION_ID,
  USER_ID,
  CUST_ID,
  USECUST_ID,
  BRAND_CODE,
  PRODUCT_ID,
  EPARCHY_CODE,
  CITY_CODE,
  USER_PASSWD,
  USER_DIFF_CODE,
  USER_TYPE_CODE,
  SERIAL_NUMBER,
  NET_TYPE_CODE,
  SCORE_VALUE,
  CREDIT_CLASS,
  BASIC_CREDIT_VALUE,
  CREDIT_VALUE,
  ACCT_TAG,
  PREPAY_TAG,
  IN_DATE,
  OPEN_DATE,
  OPEN_MODE,
  OPEN_DEPART_ID,
  OPEN_STAFF_ID,
  IN_DEPART_ID,
  IN_STAFF_ID,
  REMOVE_TAG,
  DESTROY_TIME,
  REMOVE_EPARCHY_CODE,
  REMOVE_CITY_CODE,
  REMOVE_DEPART_ID,
  REMOVE_REASON_CODE,
  PRE_DESTROY_TIME,
  FIRST_CALL_TIME,
  LAST_STOP_TIME,
  USER_STATE_CODESET,
  MPUTE_MONTH_FEE,
  MPUTE_DATE,
  UPDATE_TIME,
  ASSURE_CUST_ID,
  ASSURE_TYPE_CODE,
  ASSURE_DATE,
  DEVELOP_STAFF_ID,
  DEVELOP_DATE,
  DEVELOP_EPARCHY_CODE,
  DEVELOP_CITY_CODE,
  DEVELOP_DEPART_ID,
  DEVELOP_NO,
  REMARK,
  CREDIT_RULE_ID,
  CONTRACT_ID,
  CHANGEUSER_DATE,
  IN_NET_MODE,
  PRODUCT_TYPE_CODE,
  MAIN_DISCNT_CODE,
  PRODUCT_SPEC,
  PROVINCE_CODE,
  OPT,
  OPTTIME,
  CDHTIME,
  DATASOURCE,
  IN_TIME,
  DATABASE_TAG,
  EVENT_TIME,
  KAFKA_TIME,
  PAIMON_TIME,
  HEADERS
)
SELECT
   t1.PARTITION_ID,
  t1.USER_ID,
  t1.CUST_ID,
  t1.USECUST_ID,
  t1.BRAND_CODE,
  t1.PRODUCT_ID,
  t1.EPARCHY_CODE,
  t1.CITY_CODE,
  t1.USER_PASSWD,
  t1.USER_DIFF_CODE,
  t1.USER_TYPE_CODE,
  t1.SERIAL_NUMBER,
  t1.NET_TYPE_CODE,
  t1.SCORE_VALUE,
  t1.CREDIT_CLASS,
  t1.BASIC_CREDIT_VALUE,
  t1.CREDIT_VALUE,
  t1.ACCT_TAG,
  t1.PREPAY_TAG,
  COALESCE(t2.IN_DATE, t1.IN_DATE) AS IN_DATE,
  COALESCE(t2.OPEN_DATE, t1.OPEN_DATE) AS OPEN_DATE,
  t1.OPEN_MODE,
  t1.OPEN_DEPART_ID,
  t1.OPEN_STAFF_ID,
  t1.IN_DEPART_ID,
  t1.IN_STAFF_ID,
  t1.REMOVE_TAG,
  COALESCE(t2.DESTROY_TIME, t1.DESTROY_TIME) AS DESTROY_TIME,
  t1.REMOVE_EPARCHY_CODE,
  t1.REMOVE_CITY_CODE,
  t1.REMOVE_DEPART_ID,
  t1.REMOVE_REASON_CODE,
  t1.PRE_DESTROY_TIME,
  t1.FIRST_CALL_TIME,
  t1.LAST_STOP_TIME,
  t1.USER_STATE_CODESET,
  t1.MPUTE_MONTH_FEE,
  t1.MPUTE_DATE,
  t1.UPDATE_TIME,
  t1.ASSURE_CUST_ID,
  t1.ASSURE_TYPE_CODE,
  t1.ASSURE_DATE,
  t1.DEVELOP_STAFF_ID,
  t1.DEVELOP_DATE,
  t1.DEVELOP_EPARCHY_CODE,
  t1.DEVELOP_CITY_CODE,
  t1.DEVELOP_DEPART_ID,
  t1.DEVELOP_NO,
  t1.REMARK,
  t1.CREDIT_RULE_ID,
  t1.CONTRACT_ID,
  t1.CHANGEUSER_DATE,
  t1.IN_NET_MODE,
  t1.PRODUCT_TYPE_CODE,
  t1.MAIN_DISCNT_CODE,
  t1.PRODUCT_SPEC,
  t1.PROVINCE_CODE,
  t1.OPT,
  t1.OPTTIME,
  t1.CDHTIME,
  t1.DATASOURCE,
  t1.IN_TIME,
  t1.DATABASE_TAG,
  t1.EVENT_TIME,
  t1.KAFKA_TIME,
  t1.PAIMON_TIME,
  t1.HEADERS
FROM `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_tf_f_user` t1
INNER JOIN `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_user_info_temp` t2
    ON t1.USER_ID = t2.USER_ID;

-- 5. 数据验证查询（可选执行）
-- 验证新表中的数据
SELECT
    COUNT(*) AS total_records,
    COUNT(CASE WHEN IN_DATE IS NOT NULL THEN 1 END) AS records_with_in_date,
    COUNT(CASE WHEN OPEN_DATE IS NOT NULL THEN 1 END) AS records_with_open_date,
    COUNT(CASE WHEN DESTROY_TIME IS NOT NULL THEN 1 END) AS records_with_destroy_time
FROM `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_user_info_temp`;

-- 验证更新后的数据
SELECT
    COUNT(*) AS updated_records
FROM `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_tf_f_user` t1
INNER JOIN `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_user_info_temp` t2
    ON t1.USER_ID = t2.USER_ID;

-- 6. 清理临时表（可选，根据需要决定是否执行）
-- DROP TABLE IF EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_user_info_temp`;
