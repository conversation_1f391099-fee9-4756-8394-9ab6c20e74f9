-- Paimon配置修复脚本
-- 解决"Paimon Sink does not support Flink's Adaptive Parallelism mode"错误

-- 使用Paimon Catalog
USE CATALOG paimon_catalog;

-- ========================================
-- 方案1：设置Flink运行时配置
-- ========================================

-- 关闭自适应并行度模式
SET 'jobmanager.adaptive-scheduler.enabled' = 'false';

-- 设置为批处理模式
SET 'execution.runtime-mode' = 'BATCH';

-- 设置检查点配置
SET 'execution.checkpointing.mode' = 'EXACTLY_ONCE';
SET 'execution.checkpointing.interval' = '10s';

-- 设置表执行配置
SET 'table.exec.sink.not-null-enforcer' = 'DROP';
SET 'table.exec.sink.upsert-materialize' = 'NONE';

-- 设置管道配置
SET 'pipeline.operator-chaining' = 'true';

-- ========================================
-- 方案2：为现有表添加sink.parallelism配置
-- ========================================

-- 如果需要修改现有表的配置，可以使用ALTER TABLE语句
-- ALTER TABLE `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_user_info_temp` 
-- SET ('sink.parallelism' = '4');

-- ALTER TABLE `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_tf_f_user` 
-- SET ('sink.parallelism' = '4');

-- ========================================
-- 方案3：重新创建表（如果需要）
-- ========================================

-- 删除现有临时表（如果存在问题）
-- DROP TABLE IF EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_user_info_temp`;

-- 重新创建临时表，明确指定sink.parallelism
CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_user_info_temp_v2` (
    USER_ID STRING NOT NULL,
    IN_DATE STRING,
    OPEN_DATE STRING,
    DESTROY_TIME STRING,
    paimon_ingest_time TIMESTAMP(3) COMMENT 'Paimon 记录写入处理时间戳',
    PRIMARY KEY (USER_ID) NOT ENFORCED
) WITH (
    'connector' = 'paimon',
    'path' = 'hdfs:///user/hh_slfn2_sschj/paimon/ubd_sscj_prod_flink/dwd_r_paimon_user_info_temp_v2',
    'auto-create' = 'true',
    'merge-engine' = 'partial-update',
    'sequence.field' = 'paimon_ingest_time',
    'partial-update.ignore-delete' = 'true',
    'changelog-producer' = 'input',
    'bucket' = '64',
    'snapshot.time-retained' = '6h',
    'file.format' = 'avro',
    'metadata.stats-mode' = 'none',
    'consumer.expiration-time' = '72h',
    'sink.parallelism' = '4'
);

-- ========================================
-- 验证配置
-- ========================================

-- 查看当前Flink配置
SHOW CONFIGURATION;

-- 查看表属性
DESCRIBE `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_user_info_temp_v2`;

-- ========================================
-- 使用说明
-- ========================================

/*
使用建议：

1. 首先执行方案1中的SET语句来配置Flink运行时参数

2. 如果仍然报错，可以尝试方案2修改现有表的配置

3. 如果问题持续，使用方案3重新创建表

4. 在执行INSERT语句之前，确保所有配置都已正确设置

5. 如果在Flink SQL Client中执行，可能需要重启客户端以使配置生效

常见的解决方法：
- 设置 'jobmanager.adaptive-scheduler.enabled' = 'false'
- 设置 'execution.runtime-mode' = 'BATCH'
- 在表的WITH子句中添加 'sink.parallelism' = '4'
- 确保Flink版本与Paimon版本兼容
*/
